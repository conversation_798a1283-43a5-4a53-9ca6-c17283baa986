<?php /*a:2:{s:95:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\admin\cdkey.html";i:1752139242;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1751973426;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    console.log('toggleMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    console.log('sidebar:', sidebar);
    console.log('overlay:', overlay);

    if (!sidebar) {
      console.error('Sidebar not found!');
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      console.log('Hiding sidebar');
      hideMobileSidebar();
    } else {
      console.log('Showing sidebar');
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    console.log('showMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
      console.log('Added mobile-show class to sidebar');
    }

    if (overlay) {
      overlay.classList.add('show');
      console.log('Added show class to overlay');
    }
  }

  function hideMobileSidebar() {
    console.log('hideMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
      console.log('Removed mobile-show class from sidebar');
    }

    if (overlay) {
      overlay.classList.remove('show');
      console.log('Removed show class from overlay');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
			border: 1px solid #ddd !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
			background: #fff !important;
			padding: 10px !important;
		}

		/* 确保侧边栏菜单项可见 */
		#admin-offcanvas .admin-sidebar-list {
			background: #fff !important;
			margin: 0 !important;
			padding: 0 !important;
		}

		#admin-offcanvas .admin-sidebar-list li {
			background: #fff !important;
			border-bottom: 1px solid #eee !important;
		}

		#admin-offcanvas .admin-sidebar-list li a {
			color: #333 !important;
			padding: 12px 15px !important;
			display: block !important;
			text-decoration: none !important;
		}

		#admin-offcanvas .admin-sidebar-list li a:hover {
			background: #f5f5f5 !important;
			color: #1E9FFF !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
			color: white !important;
			border: none !important;
			padding: 6px 12px !important;
			border-radius: 3px !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 确保移动端菜单按钮在右侧正确显示 */
		.am-topbar-right .am-show-sm-only {
			float: right;
		}

		.am-topbar-right .mobile-sidebar-toggle {
			margin: 8px 10px 8px 0;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>

	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
			<!-- 移动设备侧边栏切换按钮 - 放在最右边 -->
			<li class="am-show-sm-only">
				<button class="am-topbar-btn am-btn am-btn-sm am-btn-primary mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
					<span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
				</button>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<!-- <li><a href="/manage/help"><span class="am-icon-map-signs"></span> 防骗指南</a></li> -->
			<!-- <li><a href="/manage/choose"><span class="am-icon-paint-brush"></span> 安全测试</a></li> -->
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		  <div class="am-panel am-panel-default admin-sidebar-panel">
			<div class="am-panel-bd">
			  <p><span class="am-icon-bookmark"></span> 公告</p>
			  <div class="line">
                    <span>萌新必看：</span>
                    <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/9839e342f1dd89219e2e2980a9a803a42b9d94cf">2.0.1 使用指南</a></p>
			        <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/7ea1c83b63c8b59b7472c999d15156c4fb843d31">3.0 新版使用指南</a></p>
			        
               </div>
			  
			  <div class="line">
                    <span>剑灵小助手[3.0]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/94255b808597">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1qcuS-obYbHKBQAuuH6_Bhw?pwd=5210">百度网盘</a>
                    </p>
               </div>
               <div class="line">
                    <span>剑灵小助手[2.0.1]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/2ad567e2b816#/list/share">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1irL97YxJfR1-UWxkxqXHIQ?pwd=5210">百度网盘</a>
                    <a class="home" target="_blank" href="https://www.lanzoul.com/iUyTU1irepef">蓝奏云</a>
                    </p>
                </div>
			  
			  <div class="texts" style="margin-top: 20px;">
                    <p><span>助手①群：<code>548810086</code></span></p>
                    <p><span>助手②群：<code>563768233</code></span></p>
                    <p><span>助手③群：<code>618986361</code></span></p>
                </div>
			</div>
		  </div>
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding">
			<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">首页</strong> / <small>CDkey生成</small></div>
			<div class="am-fr">
				<a href="/manage/admin/downloads" class="am-btn am-btn-secondary am-btn-sm">
					<span class="am-icon-download"></span> 下载管理
				</a>
			</div>
		</div>
		<div>
			<form class="am-form am-form-horizontal" method="post" id="InfoForm" style='text-align: center;'>
				<div class="am-form-group" id="type">
					<label for="type" class="am-u-sm-3 am-form-label">选择类型</label>
					<div class="am-u-sm-9">
						<select id="type">
							<option value="customize">● 装备查询自定义 (此功能使用就永久)</option>
							<option value="client">● 战斗记录时长</option>
							<option value="drawtimes">● 额外抽奖次数</option>
						</select>
						<small id="type_msg"></small>
					</div>
				</div>
				<div class="am-form-group" id="specified-info">
					<div class="am-form-group" id="time-limit-type">
						<label for="time-limit-type" class="am-u-sm-3 am-form-label">选择期限类型</label>
						<div class="am-u-sm-9">
							<select id="time-limit-type">
								<option value="fixed">● 固定日期结束</option>
								<option value="duration">● 激活后开始计算天数</option>
							</select>
							<small id="time-limit-type_msg"></small>
						</div>
					</div>
					<!-- 激活天数方式，默认隐藏 -->
					<div class="am-form-group" style="display:none" id="usable-duration">
						<label for="usable-duration" class="am-u-sm-3 am-form-label">激活天数</label>
						<div class="am-u-sm-9">
							<input type="text" id="usable-duration" name="usable-duration" value="" placeholder="激活天数，从用户激活时开始计算结束时间。" required />
							<small id="addTime_msg"></small>
						</div>
					</div>
					<!-- 固定时间方式 -->
					<div class="am-form-group" id="fixed-expiration-time">
						<label for="fixed-expiration-time" class="am-u-sm-3 am-form-label">过期时间</label>
						<div class="am-u-sm-9">
							<input type="date" id="fixed-expiration-time" name="fixed-expiration-time" value="" placeholder="页面于何时固定过期" required />
							<small id="toDate_msg"></small>
						</div>
					</div>
					<div class="am-form-group" style="display:none" id="specified-value">
						<label for="specified-value" class="am-u-sm-3 am-form-label">数值</label>
						<div class="am-u-sm-9">
							<input type="text" id="specified-value" name="specified-value" value="" placeholder="" required />
							<small id="toDate_msg"></small>
						</div>
					</div>
				</div>
				<div class="am-form-group" id="changeTimes">
					<label for="changeTimes" class="am-u-sm-3 am-form-label">∞可修改次数</label>
					<div class="am-u-sm-9">
						<input type="text" id="changeTimes" name="changeTimes" value="" placeholder="可修改次数，≤3 或不填 都默认为3次，填写∞则无限" required>
						<small id="changeTimes_msg"></small>
					</div>
				</div>
				<div class="am-form-group" id="usableTimes">
					<label for="usableTimes" class="am-u-sm-3 am-form-label">可使用次数</label>
					<div class="am-u-sm-9">
						<input type="text" id="usableTimes" name="usableTimes" value="1" placeholder="批量生成时固定为1次" readonly style="background-color: #f5f5f5;">
						<small id="usableTimes_msg" style="color: #ff6600;">注意：批量生成时可使用次数固定为1次</small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="startTime" class="am-u-sm-3 am-form-label">激活开放时间</label>
					<div class="am-u-sm-9">
						<input type="date" id="startTime" name="startTime" value="" placeholder="激活截止时间" required>
						<small id="startTime_msg"></small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="endTime" class="am-u-sm-3 am-form-label">激活结束时间</label>
					<div class="am-u-sm-9">
						<input type="date" id="endTime" name="endTime" value="" placeholder="激活截止时间" required>
						<small id="endTime_msg"></small>
					</div>
				</div>
				<!-- 批次字段已隐藏，批量生成不使用批次信息 -->
				<div class="am-form-group">
					<label for="num" class="am-u-sm-3 am-form-label">生成个数</label>
					<div class="am-u-sm-9">
						<input type="text" id="num" name="num" value="" placeholder="生成个数,不填写默认1个,一次生成不得大于5000个" required>
						<small id="num_msg"></small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="discern" class="am-u-sm-3 am-form-label">识别前缀</label>
					<div class="am-u-sm-9">
						<input type="text" id="discern" name="discern" value="" placeholder="用于追加在CDkey前的标识,方便区分CDkey来源" required>
						<small id="discern_msg" style="color: #0066cc;">
							智能建议：
							<a href="javascript:void(0)" onclick="setPrefix('VIP')" class="prefix-suggestion">VIP</a> |
							<a href="javascript:void(0)" onclick="setPrefix('GIFT')" class="prefix-suggestion">GIFT</a> |
							<a href="javascript:void(0)" onclick="setPrefix('EVENT')" class="prefix-suggestion">EVENT</a> |
							<a href="javascript:void(0)" onclick="setPrefix('PROMO')" class="prefix-suggestion">PROMO</a> |
							<a href="javascript:void(0)" onclick="setPrefix('TEST')" class="prefix-suggestion">TEST</a>
						</small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="remark" class="am-u-sm-3 am-form-label">备注</label>
					<div class="am-u-sm-9">
						<textarea type="text" id="remark" name="remark"></textarea>
						<small id="remark_msg"></small>
					</div>
				</div>
				<button type="button" id="Btn_Submit" onclick="create();" class="am-btn am-btn-primary">生成</button>
			</form>
		</div>
		<hr>
		<div class="am-g">
			<div class="am-u-sm-12 am-u-md-3">
				<div class="am-input-group am-input-group-sm">
					<input type="text" class="am-form-field" id="search_value" placeholder="输入需要查询的信息">
					<span class="am-input-group-btn">
						<button class="am-btn am-btn-default" type="button" id="search" onclick="search();">查询</button>
					</span>
				</div>
			</div>
			<div class="am-select am-u-sm-9">
				<select class="ModeSelect">
					<option value="0">全部</option>
					<option value="1">当前可用</option>
					<option value="2">当前不可用</option>
				</select>
				<span style="padding-left:20px;"><input type="checkbox" class="show-alluser" style="margin-left:10px;" />查看所有（不勾选表示只显示自己）</p> </span>
			</div>
		</div>
		<div class="am-g">
			<div class="am-u-sm-12">
				<table class="am-table am-table-bd am-table-striped admin-content-table">
					<thead>
						<tr>
							<th>ID</th>
							<th>CDkey</th>
							<th>生成者</th>
							<th>总次数</th>
							<th>当前剩余</th>
							<th>激活开始时间</th>
							<th>激活截止时间</th>
							<th>类型</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody>
					<?php if(is_array($data) || $data instanceof \think\Collection || $data instanceof \think\Paginator): $i = 0; $__LIST__ = $data;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$item): $mod = ($i % 2 );++$i;?>
						<tr>
							<td><?php echo htmlentities((string) $item['id']); ?></td>
							<td><?php echo htmlentities((string) $item['cdkey']); ?></td>
							<td><?php echo isset($item['username']) ? htmlentities((string) $item['username']) : "系统"; ?></td>
							<td><?php echo htmlentities((string) $item['MaxNum']); ?></td>
							<td><?php echo htmlentities((string) $item['RemainNum']); ?></td>
							<td><?php echo htmlentities((string) $item['startTime']); ?></td>
							<td><?php echo htmlentities((string) $item['endTime']); ?></td>
							<td><?php echo htmlentities((string) $item['type']); ?></td>
							<td>
								<div class="am-btn-group am-btn-group-xs">
									<a href="?id=<?php echo htmlentities((string) $item['id']); ?>" class="am-btn am-btn-default am-btn-xs am-text-secondary"><span class="am-icon-pencil-square-o"></span> 编辑</a>
								</div>
							</td>
						</tr>
					<?php endforeach; endif; else: echo "" ;endif; ?>
                    </tbody>
				</table>
			</div>
		</div>
    </div>
</div>

<style>
.prefix-suggestion {
	color: #0066cc;
	text-decoration: none;
	padding: 2px 4px;
	border-radius: 3px;
	background-color: #f0f8ff;
	margin: 0 2px;
	font-size: 12px;
}

.prefix-suggestion:hover {
	background-color: #0066cc;
	color: white;
	text-decoration: none;
}

#discern_msg {
	margin-top: 5px;
	font-size: 12px;
}
</style>

<script>
	//回车事件绑定		
	$('#search_value').bind('keypress', function(event) {
		if (event.keyCode == "13") {
			event.preventDefault();
	
			//回车执行查询
			search();
		}
	});
	
	function create() {
	
		if (!confirm("是否确认进行操作?")) return;
	
		$.ajax({
			type: "POST", //方法类型
			dataType: "json", //预期服务器返回的数据类型
			url: window.location.href, //url
			data: $('#InfoForm').serialize() 
				+ "&type=" + $('select#type').val()
				+ "&time-limit-type=" + $('select#time-limit-type').val(),
	
			success: function(result) {
				if (result.code) {
					var message = result.msg;
					if (result.download_url) {
						message += "\n\n点击确定后将自动下载CDkey文件";
					}
					alert(message);

					// 如果有下载链接，自动下载文件
					if (result.download_url) {
						var link = document.createElement('a');
						link.href = result.download_url;
						link.download = result.filename || 'cdkeys.txt';
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
					}

					location.reload();
				} else {
					alert(result.msg);
				}
			},
			error: function() {
				alert("提交失败,未知错误");
			}
		});
	}

	// 设置识别前缀
	function setPrefix(prefix) {
		$('#discern').val(prefix);
		$('#discern').focus();
	}

	// 智能前缀建议
	function updatePrefixSuggestions() {
		var type = $('#type').val();
		var suggestions = [];

		switch(type) {
			case 'customize':
				suggestions = ['VIP', 'CUSTOM', 'SPECIAL', 'PREMIUM', 'GOLD'];
				break;
			case 'client':
				suggestions = ['CLIENT', 'EQUIP', 'GEAR', 'TOOL', 'HELPER'];
				break;
			case 'drawtimes':
				suggestions = ['DRAW', 'LUCKY', 'LOTTERY', 'CHANCE', 'SPIN'];
				break;
			default:
				suggestions = ['VIP', 'GIFT', 'EVENT', 'PROMO', 'TEST'];
		}

		var html = '智能建议：';
		for (var i = 0; i < suggestions.length; i++) {
			html += '<a href="javascript:void(0)" onclick="setPrefix(\'' + suggestions[i] + '\')" class="prefix-suggestion">' + suggestions[i] + '</a>';
			if (i < suggestions.length - 1) html += ' | ';
		}

		$('#discern_msg').html(html);
	}

	// 自动生成带时间戳的前缀
	function generateTimestampPrefix() {
		var type = $('#type').val();
		var typePrefix = '';

		switch(type) {
			case 'customize': typePrefix = 'CUSTOM'; break;
			case 'client': typePrefix = 'CLIENT'; break;
			case 'drawtimes': typePrefix = 'DRAW'; break;
			default: typePrefix = 'CDK';
		}

		var now = new Date();
		var timestamp = (now.getMonth() + 1).toString().padStart(2, '0') +
						now.getDate().toString().padStart(2, '0') +
						now.getHours().toString().padStart(2, '0') +
						now.getMinutes().toString().padStart(2, '0');

		return typePrefix + timestamp;
	}


	function search() {
		var search_value = $("#search_value").val();
	
		if (search_value == null || search_value == undefined || search_value == '') {
	
			alert("请输入查询条件 如服务器:无日峰 或角色名称 以及任意你知道的信息");
			return false;
	
		} else {
	
			window.location.href = "?search=" + search_value;
	
		}
	}
	

	$('select#type').change(function() {
		$('.am-form-group #specified-info').hide();
		$('#changeTimes').hide();
		$('#specified-value').hide();

		switch ($(this).val()) {
			case 'customize':
				$('#time-limit-type').show();
				$('#changeTimes').show();
				break;

			case 'client':
				$('#time-limit-type').show();
				break;

			case 'drawtimes':
				$('#specified-value').show();
				break;
		}

		// 更新智能前缀建议
		updatePrefixSuggestions();

		// 如果前缀为空，自动填充智能前缀
		if ($('#discern').val() === '') {
			$('#discern').val(generateTimestampPrefix());
		}
	})
	
	//时长控制控件
	$('select#time-limit-type').change(function() {
		
		var DurationGroup = $('div#usable-duration');
		var FixedGroup = $('div#fixed-expiration-time');
	
		DurationGroup.hide();
		FixedGroup.hide();
	
		//根据情况显示组
		switch ($(this).val()) {
			case 'duration': DurationGroup.show(); break;
			case 'fixed': FixedGroup.show(); break;
		}
	})
	
	
	//*******************************************************************//
	var _progress = false;
	
	function init() {
	
		_progress = true;
	
		var mode = getQueryVariable("mode");
		var all  = getQueryVariable("all") == "true";
	
		$(".ModeSelect").find("option").each(function() {
			if ($(this).val() == mode) {
				$(".ModeSelect").val($(this).val()).trigger('change');
				return;
			}
		})
	
		//如果显示所有生成
		$(".show-alluser").attr("checked", all);

		// 初始化智能前缀建议
		updatePrefixSuggestions();

		// 如果前缀为空，自动填充智能前缀
		if ($('#discern').val() === '') {
			$('#discern').val(generateTimestampPrefix());
		}

		_progress = false;
	}
	
	init();
	//*******************************************************************//

	// 实现模式切换
	$('.ModeSelect,.show-alluser').change(function() {
		if (_progress) return;
	
		var mode = $(".ModeSelect").val();
		var all = $(".show-alluser").is(':checked');
	
		if (mode == null || mode == "" || mode == "undefined") location.reload();
		else window.location.href = "?mode=" + mode + "&all=" + all;
	})
</script>

/* 表格区域样式 */
.admin-content-table {
    width: 100%;
    margin-top: 20px;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-content-table thead th {
    background: #f8f9fa;
    padding: 15px;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #eee;
    text-align: left;
}

.admin-content-table tbody td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    color: #4a5568;
    font-size: 14px;
}

.admin-content-table tbody tr:hover {
    background: #f8f9fa;
}

.admin-content-table tbody tr:last-child td {
    border-bottom: none;
}

/* 操作按钮样式 */
.am-btn-group-xs .am-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.3s;
}

.am-text-secondary {
    color: #0e90d2;
    background: rgba(14,144,210,0.1);
    border: none;
}

.am-text-secondary:hover {
    background: rgba(14,144,210,0.2);
    color: #0e90d2;
}

/* 搜索框样式 */
.am-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.am-input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s;
}

.am-input-group input:focus {
    border-color: #0e90d2;
    box-shadow: 0 0 5px rgba(14,144,210,0.2);
    outline: none;
}

.am-input-group .am-btn {
    padding: 10px 20px;
    background: #0e90d2;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.am-input-group .am-btn:hover {
    background: #0d82bd;
}

/* 筛选区域样式 */
.am-select {
    display: flex;
    align-items: center;
    gap: 15px;
}

.ModeSelect {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    font-size: 14px;
}

.show-alluser {
    margin-left: 10px;
    accent-color: #0e90d2;
}

/* 修复颜色样式 */
.admin-content-table tbody tr {
    color: #333;
}
	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>