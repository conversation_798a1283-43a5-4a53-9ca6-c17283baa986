<?php /*a:2:{s:96:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\admin\online.html";i:1752136221;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1751973426;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    console.log('toggleMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    console.log('sidebar:', sidebar);
    console.log('overlay:', overlay);

    if (!sidebar) {
      console.error('Sidebar not found!');
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      console.log('Hiding sidebar');
      hideMobileSidebar();
    } else {
      console.log('Showing sidebar');
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    console.log('showMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
      console.log('Added mobile-show class to sidebar');
    }

    if (overlay) {
      overlay.classList.add('show');
      console.log('Added show class to overlay');
    }
  }

  function hideMobileSidebar() {
    console.log('hideMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
      console.log('Removed mobile-show class from sidebar');
    }

    if (overlay) {
      overlay.classList.remove('show');
      console.log('Removed show class from overlay');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
			border: 1px solid #ddd !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
			background: #fff !important;
			padding: 10px !important;
		}

		/* 确保侧边栏菜单项可见 */
		#admin-offcanvas .admin-sidebar-list {
			background: #fff !important;
			margin: 0 !important;
			padding: 0 !important;
		}

		#admin-offcanvas .admin-sidebar-list li {
			background: #fff !important;
			border-bottom: 1px solid #eee !important;
		}

		#admin-offcanvas .admin-sidebar-list li a {
			color: #333 !important;
			padding: 12px 15px !important;
			display: block !important;
			text-decoration: none !important;
		}

		#admin-offcanvas .admin-sidebar-list li a:hover {
			background: #f5f5f5 !important;
			color: #1E9FFF !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
			color: white !important;
			border: none !important;
			padding: 6px 12px !important;
			border-radius: 3px !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 确保移动端菜单按钮在右侧正确显示 */
		.am-topbar-right .am-show-sm-only {
			float: right;
		}

		.am-topbar-right .mobile-sidebar-toggle {
			margin: 8px 10px 8px 0;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>

	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
			<!-- 移动设备侧边栏切换按钮 - 放在最右边 -->
			<li class="am-show-sm-only">
				<button class="am-topbar-btn am-btn am-btn-sm am-btn-primary mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
					<span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
				</button>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<!-- <li><a href="/manage/help"><span class="am-icon-map-signs"></span> 防骗指南</a></li> -->
			<!-- <li><a href="/manage/choose"><span class="am-icon-paint-brush"></span> 安全测试</a></li> -->
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		  <div class="am-panel am-panel-default admin-sidebar-panel">
			<div class="am-panel-bd">
			  <p><span class="am-icon-bookmark"></span> 公告</p>
			  <div class="line">
                    <span>萌新必看：</span>
                    <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/9839e342f1dd89219e2e2980a9a803a42b9d94cf">2.0.1 使用指南</a></p>
			        <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/7ea1c83b63c8b59b7472c999d15156c4fb843d31">3.0 新版使用指南</a></p>
			        
               </div>
			  
			  <div class="line">
                    <span>剑灵小助手[3.0]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/94255b808597">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1qcuS-obYbHKBQAuuH6_Bhw?pwd=5210">百度网盘</a>
                    </p>
               </div>
               <div class="line">
                    <span>剑灵小助手[2.0.1]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/2ad567e2b816#/list/share">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1irL97YxJfR1-UWxkxqXHIQ?pwd=5210">百度网盘</a>
                    <a class="home" target="_blank" href="https://www.lanzoul.com/iUyTU1irepef">蓝奏云</a>
                    </p>
                </div>
			  
			  <div class="texts" style="margin-top: 20px;">
                    <p><span>助手①群：<code>548810086</code></span></p>
                    <p><span>助手②群：<code>563768233</code></span></p>
                    <p><span>助手③群：<code>618986361</code></span></p>
                </div>
			</div>
		  </div>
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">在线用户统计</strong> / <small>Online Users Statistics</small>
            </div>
        </div>
        <hr>

        <!-- 统计卡片 -->
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="current-online"><?php echo htmlentities((string) (isset($online_stats['online_count']) && ($online_stats['online_count'] !== '')?$online_stats['online_count']:0)); ?></div>
                    <div class="stats-label">当前在线</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="auth-users"><?php echo htmlentities((string) (isset($online_stats['auth_stats']['active_users']) && ($online_stats['auth_stats']['active_users'] !== '')?$online_stats['auth_stats']['active_users']:0)); ?></div>
                    <div class="stats-label">认证用户</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <div class="stats-number" id="active-devices"><?php echo htmlentities((string) (isset($online_stats['heartbeat_stats']['active_devices']) && ($online_stats['heartbeat_stats']['active_devices'] !== '')?$online_stats['heartbeat_stats']['active_devices']:0)); ?></div>
                    <div class="stats-label">活跃设备</div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-3">
                <div class="stats-card">
                    <?php if($online_stats['status'] == 'ok'): ?>
                    <div class="stats-number status-normal" id="system-status">正常</div>
                    <?php else: ?>
                    <div class="stats-number status-error" id="system-status">异常</div>
                    <?php endif; ?>
                    <div class="stats-label">系统状态</div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="am-g am-margin-top">
            <div class="am-u-sm-12 am-u-md-8">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        在线用户趋势
                        <div class="am-fr">
                            <button type="button" class="am-btn am-btn-xs am-btn-primary" onclick="changePeriod('hour')">小时</button>
                            <button type="button" class="am-btn am-btn-xs am-btn-default" onclick="changePeriod('day')">天</button>
                            <button type="button" class="am-btn am-btn-xs am-btn-secondary" onclick="toggleAutoRefresh()">
                                <span id="auto-refresh-text">开启自动刷新</span>
                            </button>
                            <button type="button" class="am-btn am-btn-xs am-btn-warning" onclick="debugChart()">
                                调试
                            </button>
                        </div>
                    </div>
                    <div class="am-panel-bd">
                        <canvas id="onlineChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- 在线用户列表 -->
            <div class="am-u-sm-12 am-u-md-4">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        在线用户列表
                        <button type="button" class="am-btn am-btn-xs am-btn-default am-fr" onclick="refreshUserList()">
                            <i class="am-icon-refresh"></i> 刷新
                        </button>
                    </div>
                    <div class="am-panel-bd" style="height: 400px; overflow-y: auto;">
                        <div id="online-users-list">
                            <div class="am-text-center" style="padding: 50px 0;">
                                <i class="am-icon-spinner am-icon-spin"></i>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细信息区域 -->
        <div class="am-g am-margin-top">
            <div class="am-u-sm-12">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">系统详细信息</div>
                    <div class="am-panel-bd">
                        <div class="am-g">
                            <div class="am-u-sm-12 am-u-md-6">
                                <h4>认证服务统计</h4>
                                <table class="am-table am-table-striped am-table-compact">
                                    <tbody>
                                        <tr><td>总Token数</td><td id="total-tokens"><?php echo htmlentities((string) (isset($online_stats['auth_stats']['total_tokens']) && ($online_stats['auth_stats']['total_tokens'] !== '')?$online_stats['auth_stats']['total_tokens']:0)); ?></td></tr>
                                        <tr><td>活跃用户数</td><td id="active-users-detail"><?php echo htmlentities((string) (isset($online_stats['auth_stats']['active_users']) && ($online_stats['auth_stats']['active_users'] !== '')?$online_stats['auth_stats']['active_users']:0)); ?></td></tr>
                                        <tr><td>最后更新</td><td id="auth-timestamp">未知</td></tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="am-u-sm-12 am-u-md-6">
                                <h4>心跳服务统计</h4>
                                <table class="am-table am-table-striped am-table-compact">
                                    <tbody>
                                        <tr><td>总设备数</td><td id="total-devices"><?php echo htmlentities((string) (isset($online_stats['heartbeat_stats']['total_devices']) && ($online_stats['heartbeat_stats']['total_devices'] !== '')?$online_stats['heartbeat_stats']['total_devices']:0)); ?></td></tr>
                                        <tr><td>活跃设备数</td><td id="active-devices-detail"><?php echo htmlentities((string) (isset($online_stats['heartbeat_stats']['active_devices']) && ($online_stats['heartbeat_stats']['active_devices'] !== '')?$online_stats['heartbeat_stats']['active_devices']:0)); ?></td></tr>
                                        <tr><td>非活跃设备数</td><td id="inactive-devices"><?php echo htmlentities((string) (isset($online_stats['heartbeat_stats']['inactive_devices']) && ($online_stats['heartbeat_stats']['inactive_devices'] !== '')?$online_stats['heartbeat_stats']['inactive_devices']:0)); ?></td></tr>
                                        <tr><td>最后更新</td><td id="heartbeat-timestamp">未知</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <script>
    $(document).ready(function(){

        // 全局变量
        var chart = null;
        var currentPeriod = 'hour';
        var autoRefreshInterval = null;
        var isAutoRefresh = false;

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 检查必要的依赖
            if (typeof $ === 'undefined') {
                console.error('jQuery未加载');
                alert('页面加载失败: jQuery未加载');
                return;
            }

            if (typeof layer === 'undefined') {
                console.error('Layer未加载');
                alert('页面加载失败: Layer未加载');
                return;
            }

            // 等待Chart.js加载完成
            var checkChart = function() {
                if (typeof Chart !== 'undefined') {
                    // 初始化图表
                    initChart();

                    // 加载在线用户列表
                    loadOnlineUsers();

                    // 每30秒自动刷新统计数据
                    setInterval(function() {
                        refreshStats();
                    }, 30000);
                } else {
                    console.log('等待Chart.js加载...');
                    setTimeout(checkChart, 100);
                }
            };

            checkChart();
        });

        // 初始化图表
        function initChart() {
            try {
                var chartElement = document.getElementById('onlineChart');
                if (!chartElement) {
                    console.error('找不到图表元素 #onlineChart');
                    layer.msg('图表初始化失败: 找不到图表容器', {icon: 2});
                    return;
                }

                var ctx = chartElement.getContext('2d');
                if (!ctx) {
                    console.error('无法获取图表上下文');
                    layer.msg('图表初始化失败: 无法获取上下文', {icon: 2});
                    return;
                }

                // 检查Chart.js是否加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js未加载');
                    layer.msg('图表初始化失败: Chart.js未加载', {icon: 2});
                    return;
                }

                chart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '在线用户数',
                            data: [],
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // 加载图表数据
                loadChartData();
            } catch (e) {
                console.error('图表初始化失败:', e);
                layer.msg('图表初始化失败: ' + e.message, {icon: 2});
            }
        }

        // 加载图表数据
        function loadChartData() {
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'chart',
                    period: currentPeriod,
                    limit: currentPeriod === 'hour' ? 24 : 30
                },
                dataType: 'json',
                success: function(response) {
                    try {
                        if (response && response.code === 1 && response.data) {
                            if (chart && chart.data) {
                                chart.data.labels = response.data.labels || [];
                                if (response.data.datasets && response.data.datasets[0]) {
                                    chart.data.datasets[0].data = response.data.datasets[0].data || [];
                                }
                                chart.update();
                            }
                        } else {
                            var errorMsg = response && response.msg ? response.msg : '未知错误';
                            layer.msg('加载图表数据失败: ' + errorMsg, {icon: 2});
                        }
                    } catch (e) {
                        console.error('处理图表数据时出错:', e);
                        layer.msg('处理图表数据时出错: ' + e.message, {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', {
                        xhr: xhr,
                        status: status,
                        error: error,
                        readyState: xhr.readyState,
                        responseText: xhr.responseText,
                        statusCode: xhr.status,
                        statusText: xhr.statusText,
                        headers: xhr.getAllResponseHeaders()
                    });

                    var errorMsg = '网络错误';
                    var debugInfo = '';

                    // 详细错误分析
                    if (xhr.status === 0) {
                        errorMsg = '网络连接失败或被阻止';
                        debugInfo = '可能是CORS问题或网络不通';
                    } else if (xhr.status === 404) {
                        errorMsg = '接口不存在 (404)';
                        debugInfo = '路由配置可能有问题';
                    } else if (xhr.status === 500) {
                        errorMsg = '服务器内部错误 (500)';
                        debugInfo = '检查PHP错误日志';
                    } else if (xhr.status === 403) {
                        errorMsg = '权限不足 (403)';
                        debugInfo = '可能需要重新登录';
                    } else if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            errorMsg = response.msg || errorMsg;
                            debugInfo = response.debug ? JSON.stringify(response.debug) : '';
                        } catch (e) {
                            errorMsg = xhr.responseText.substring(0, 100);
                            debugInfo = '响应不是有效的JSON格式';
                        }
                    }

                    console.log('错误详情:', debugInfo);
                    layer.msg('加载图表数据失败: ' + errorMsg, {icon: 2});

                    // 如果是权限问题，提示重新登录
                    if (xhr.status === 403 || errorMsg.includes('登录')) {
                        setTimeout(function() {
                            if (confirm('登录状态可能已过期，是否重新登录？')) {
                                window.location.href = '/manage/login?type=admin';
                            }
                        }, 2000);
                    }
                }
            });
        }

        // 切换时间周期
        window.changePeriod = function(period) {
            currentPeriod = period;
            loadChartData();

            // 更新按钮状态
            $('.am-panel-hd .am-btn').removeClass('am-btn-primary').addClass('am-btn-default');
            $('[onclick="changePeriod(\'' + period + '\')"]').removeClass('am-btn-default').addClass('am-btn-primary');
        };

        // 切换自动刷新
        window.toggleAutoRefresh = function() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                $('#auto-refresh-text').text('开启自动刷新');
            } else {
                autoRefreshInterval = setInterval(function() {
                    loadChartData();
                    refreshStats();
                }, 10000); // 10秒刷新一次
                isAutoRefresh = true;
                $('#auto-refresh-text').text('关闭自动刷新');
            }
        };

        // 调试图表功能
        window.debugChart = function() {
            console.log('=== 图表调试信息 ===');
            console.log('Chart对象:', chart);
            console.log('当前周期:', currentPeriod);
            console.log('自动刷新状态:', isAutoRefresh);

            // 测试直接调试接口
            console.log('测试直接调试接口...');
            $.ajax({
                url: '/debug_chart_direct.php',
                type: 'GET',
                data: {
                    period: currentPeriod,
                    limit: currentPeriod === 'hour' ? 24 : 30
                },
                dataType: 'json',
                success: function(response) {
                    console.log('直接调试响应:', response);
                    if (response.debug) {
                        console.log('调试详情:', response.debug);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('直接调试错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应文本:', xhr.responseText);
                }
            });

            // 测试API接口
            console.log('测试chart接口...');
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'chart',
                    period: currentPeriod,
                    limit: currentPeriod === 'hour' ? 24 : 30
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Chart API响应:', response);
                    layer.msg('调试信息已输出到控制台，请按F12查看', {icon: 1});
                },
                error: function(xhr, status, error) {
                    console.error('Chart API错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应状态:', xhr.status);
                    console.log('响应文本:', xhr.responseText);
                    layer.msg('API请求失败，详情请查看控制台', {icon: 2});
                }
            });

            // 测试realtime接口
            console.log('测试realtime接口...');
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'realtime'
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Realtime API响应:', response);
                },
                error: function(xhr, status, error) {
                    console.error('Realtime API错误:', {xhr: xhr, status: status, error: error});
                    console.log('响应文本:', xhr.responseText);
                }
            });
        };

        // 刷新统计数据
        function refreshStats() {
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'realtime'
                },
                dataType: 'json',
                success: function(response) {
                    try {
                        if (response && response.code === 1 && response.data) {
                            var data = response.data;

                            // 更新统计卡片
                            $('#current-online').text(data.online_count || 0);
                            if (data.auth_stats) {
                                $('#auth-users').text(data.auth_stats.active_users || 0);
                                $('#total-tokens').text(data.auth_stats.total_tokens || 0);
                                $('#active-users-detail').text(data.auth_stats.active_users || 0);

                                // 更新时间戳
                                if (data.auth_stats.timestamp) {
                                    $('#auth-timestamp').text(new Date(data.auth_stats.timestamp * 1000).toLocaleString());
                                }
                            }

                            if (data.heartbeat_stats) {
                                $('#active-devices').text(data.heartbeat_stats.active_devices || 0);
                                $('#total-devices').text(data.heartbeat_stats.total_devices || 0);
                                $('#active-devices-detail').text(data.heartbeat_stats.active_devices || 0);
                                $('#inactive-devices').text(data.heartbeat_stats.inactive_devices || 0);

                                // 更新时间戳
                                if (data.heartbeat_stats.timestamp) {
                                    $('#heartbeat-timestamp').text(new Date(data.heartbeat_stats.timestamp * 1000).toLocaleString());
                                }
                            }

                            // 更新系统状态
                            var statusText = data.status === 'ok' ? '正常' : '异常';
                            var statusColor = data.status === 'ok' ? '#5FB878' : '#FF5722';
                            $('#system-status').text(statusText).css('color', statusColor);
                        } else {
                            console.warn('刷新统计数据失败:', response);
                        }
                    } catch (e) {
                        console.error('处理统计数据时出错:', e);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('刷新统计数据请求失败:', {xhr: xhr, status: status, error: error});
                }
            });
        }

        // 加载在线用户列表
        function loadOnlineUsers() {
            $.ajax({
                url: '/manage/admin/online',
                type: 'POST',
                data: {
                    action: 'users'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        var users = response.data.users || [];
                        var html = '';
                        
                        if (users.length === 0) {
                            html = '<div class="am-text-center" style="padding: 50px 0;"><p>暂无在线用户</p></div>';
                        } else {
                            users.forEach(function(user) {
                                var loginTime = new Date(user.login_time * 1000).toLocaleString();
                                html += '<div class="am-panel am-panel-default am-margin-bottom-sm">';
                                html += '<div class="am-panel-bd" style="padding: 10px;">';
                                html += '<div><strong>QQ: ' + user.uin + '</strong></div>';
                                html += '<div class="am-text-sm am-text-secondary">登录时间: ' + loginTime + '</div>';
                                html += '</div></div>';
                            });
                        }

                        $('#online-users-list').html(html);
                    } else {
                        $('#online-users-list').html('<div class="am-text-center" style="padding: 50px 0;"><p>加载失败</p></div>');
                    }
                },
                error: function() {
                    $('#online-users-list').html('<div class="am-text-center" style="padding: 50px 0;"><p>网络错误</p></div>');
                }
            });
        }

        // 刷新用户列表
        window.refreshUserList = function() {
            loadOnlineUsers();
        };
    });
    </script>

    <style>
    .stats-card {
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .stats-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 5px;
        color: #0e90d2;
    }

    .stats-number.status-normal {
        color: #5cb85c;
    }

    .stats-number.status-error {
        color: #d9534f;
    }

    .stats-label {
        color: #999;
        font-size: 0.9em;
        margin: 0;
    }

    .am-panel-bd canvas {
        max-width: 100%;
        height: auto;
    }
    </style>

	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>