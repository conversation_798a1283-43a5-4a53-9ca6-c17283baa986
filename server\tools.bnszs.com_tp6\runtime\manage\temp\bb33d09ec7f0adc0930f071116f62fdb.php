<?php /*a:2:{s:103:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\update_config\index.html";i:1751992426;s:99:"E:\Build\C++\bnspatch\src\BnsPlugin\server\tools.bnszs.com_tp6\app\manage\view\manage\template.html";i:1751973426;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <!-- 项目构建：兔子、0x1ng、Xylia  | 项目创建:2020-06-01  | 项目更新:2025-02-05 -->
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="剑灵小助手管理系统。">
  <meta name="keywords" content="剑灵骗子,剑灵骗子大全,游戏骗子,剑灵骗子数据库,小助手骗子数据库,小助手提交骗子,小助手自定义,装备查询优化,装备查询自定义,小助手装备查询,剑灵装备查询,剑灵小助手">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="renderer" content="webkit">
  <meta name="apple-mobile-web-app-title" content="Amaze UI" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />

  <title>剑灵小助手管理系统</title>
  <link rel="icon shortcut" href="/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.css">
  <link rel="stylesheet" href="/css/manage.css?version=2025021504"/>
  <link rel="stylesheet" href="/css/admin.css?version=2021021110">
  <link rel="stylesheet" href="/css/tally.css?version=2021021112">

  <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.min.js"></script>
  <script src="https://static-1251192097.cos.ap-shanghai.myqcloud.com/web_html/assets/layer/layer.js"></script>
  <script src="/js/amazeui.min.js"></script>
  <script src="/js/manage.js"></script>

  <script>
  // 移动设备侧边栏控制函数 - 全局定义
  function toggleMobileSidebar() {
    console.log('toggleMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    console.log('sidebar:', sidebar);
    console.log('overlay:', overlay);

    if (!sidebar) {
      console.error('Sidebar not found!');
      return;
    }

    if (sidebar.classList.contains('mobile-show')) {
      console.log('Hiding sidebar');
      hideMobileSidebar();
    } else {
      console.log('Showing sidebar');
      showMobileSidebar();
    }
  }

  function showMobileSidebar() {
    console.log('showMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.add('mobile-show');
      console.log('Added mobile-show class to sidebar');
    }

    if (overlay) {
      overlay.classList.add('show');
      console.log('Added show class to overlay');
    }
  }

  function hideMobileSidebar() {
    console.log('hideMobileSidebar called');
    var sidebar = document.getElementById('admin-offcanvas');
    var overlay = document.querySelector('.mobile-sidebar-overlay');

    if (sidebar) {
      sidebar.classList.remove('mobile-show');
      console.log('Removed mobile-show class from sidebar');
    }

    if (overlay) {
      overlay.classList.remove('show');
      console.log('Removed show class from overlay');
    }
  }
  </script>

  <style type="text/css">
  	.ripple {
		position: relative;
		overflow: hidden;
	}
			
	.ripple:after {
		content: "";
		display: block;
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		pointer-events: none;
		background-image: radial-gradient(circle, #666 10%, transparent 10.01%);
		background-repeat: no-repeat;
		background-position: 50%;
		transform: scale(10, 10);
		opacity: 0;
		transition: transform .3s, opacity .5s;
	}
			
	.ripple:active:after {
		transform: scale(0, 0);
		opacity: .3;
		transition: 0s;
	}

	.btn-sign{
		border-width:0px;
		width: 80px;
		height: 80px;
	}
			
	.option {
		width: 200px;
		height: 40px;
		border: 1px solid #cccccc;
		position: relative;
	}

	.option select {
		border: none;
		outline: none;
		width: 100%;
		height: 40px;
		line-height: 40px;
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		padding-left: 20px;
	}

	.option:after {
		content: "";
		width: 14px;
		height: 8px;
		background: url(/assets/arrow-down.png) no-repeat center;
		position: absolute;
		right: 20px;
		top: 41%;
		pointer-events: none;
	}

	/* 统计卡片样式 */
	.stats-card {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		margin-bottom: 15px;
		transition: all 0.3s ease;
	}

	.stats-card:hover {
		background: #e9ecef;
		transform: translateY(-2px);
		box-shadow: 0 4px 8px rgba(0,0,0,0.1);
	}

	.stats-number {
		font-size: 2.5em;
		font-weight: bold;
		color: #007bff;
		margin-bottom: 5px;
	}

	.stats-label {
		color: #6c757d;
		font-size: 0.9em;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	/* 统计卡片链接样式 */
	.stats-card-link {
		display: block;
		text-decoration: none;
		color: inherit;
	}

	.stats-card-link:hover {
		text-decoration: none;
		color: inherit;
	}

	/* 状态颜色 */
	.status-normal { color: #28a745; }
	.status-banned { color: #dc3545; }
	.status-premium { color: #ffc107; }
	.status-online { color: #17a2b8; }

	/* 移动设备侧边栏优化 */
	@media only screen and (max-width: 640px) {
		#admin-offcanvas {
			position: fixed !important;
			left: -260px !important;
			top: 51px !important;
			bottom: 0 !important;
			z-index: 1600 !important;
			transition: left 0.3s ease !important;
			background: #fff !important;
			box-shadow: 2px 0 5px rgba(0,0,0,0.1) !important;
			width: 260px !important;
			height: calc(100vh - 51px) !important;
			overflow-y: auto !important;
			border: 1px solid #ddd !important;
		}

		#admin-offcanvas.mobile-show {
			left: 0 !important;
		}

		/* 确保侧边栏内容可见 */
		#admin-offcanvas .am-offcanvas-bar {
			position: static !important;
			transform: none !important;
			width: 100% !important;
			height: 100% !important;
			background: #fff !important;
			padding: 10px !important;
		}

		/* 确保侧边栏菜单项可见 */
		#admin-offcanvas .admin-sidebar-list {
			background: #fff !important;
			margin: 0 !important;
			padding: 0 !important;
		}

		#admin-offcanvas .admin-sidebar-list li {
			background: #fff !important;
			border-bottom: 1px solid #eee !important;
		}

		#admin-offcanvas .admin-sidebar-list li a {
			color: #333 !important;
			padding: 12px 15px !important;
			display: block !important;
			text-decoration: none !important;
		}

		#admin-offcanvas .admin-sidebar-list li a:hover {
			background: #f5f5f5 !important;
			color: #1E9FFF !important;
		}

		.admin-content {
			margin-left: 0 !important;
		}

		.mobile-sidebar-toggle {
			background: #1E9FFF !important;
			border-color: #1E9FFF !important;
			color: white !important;
			border: none !important;
			padding: 6px 12px !important;
			border-radius: 3px !important;
		}

		.mobile-sidebar-toggle:hover {
			background: #0e7ce8 !important;
			border-color: #0e7ce8 !important;
		}

		/* 确保移动端菜单按钮在右侧正确显示 */
		.am-topbar-right .am-show-sm-only {
			float: right;
		}

		.am-topbar-right .mobile-sidebar-toggle {
			margin: 8px 10px 8px 0;
		}

		/* 遮罩层 */
		.mobile-sidebar-overlay {
			position: fixed;
			top: 51px;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0,0,0,0.5);
			z-index: 1500;
			display: none;
		}

		.mobile-sidebar-overlay.show {
			display: block;
		}
	}
  </style>
</head>
<body>
	<header class="am-topbar am-topbar-inverse admin-header">
	  <div class="am-topbar-brand">
		<strong>剑灵小助手管理系统</strong> <small> 堕络</small>
	  </div>

	  <div class="am-collapse am-topbar-collapse" id="topbar-collapse">
		<ul class="am-nav am-nav-pills am-topbar-nav am-topbar-right admin-header-list">
			<?php if(session("admin")): ?>
			<li class="am-dropdown" data-am-dropdown>
				<a class="am-dropdown-toggle" data-am-dropdown-toggle href="javascript:void(0);" onclick="SwitchPanel('#admin-dropdown-content')">
				  <span class="am-icon-admin"></span> 管理员 <span class="am-icon-caret-down"></span>
				</a>
				<ul class="am-dropdown-content" id="admin-dropdown-content">
				  <li><a href="javascript:void(0);" onclick="window.location.href='/admin/userinfo.php?id=1'"><span class="am-icon-admin"></span> 资料</a></li>
				  <li><a href="javascript:void(0);" onclick="logout()"><span class="am-icon-power-off"></span> 退出</a></li>
				</ul>
			  </li>
			<?php endif; ?>
			<li class="am-hide-sm-only">
				<?php if(session("user")): ?>
				<a href="/manage/center" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">个人中心</span>
				</a>
				<?php else: ?>
				<a href="/manage/login" id="user">
					<span class="am-icon-users"></span>
					<span class="admin-fullText">登录账号</span>
				</a>
				<?php endif; ?>
			</li>
			<!-- 移动设备侧边栏切换按钮 - 放在最右边 -->
			<li class="am-show-sm-only">
				<button class="am-topbar-btn am-btn am-btn-sm am-btn-primary mobile-sidebar-toggle" onclick="toggleMobileSidebar()">
					<span class="am-sr-only">菜单</span> <span class="am-icon-navicon"></span>
				</button>
			</li>
		</ul>
	  </div>
	</header>

	<div class="am-cf admin-main">
	  <!-- sidebar start -->
	  <div class="admin-sidebar am-offcanvas" id="admin-offcanvas">
		<div class="am-offcanvas-bar admin-offcanvas-bar">
		  <ul class="am-list admin-sidebar-list">
			<li><a href="/manage"><span class="am-icon-home"></span> 系统首页</a></li>
			<!-- <li><a href="/manage/liars"><span class="am-icon-th"></span> 骗子列表 <span class="am-badge am-badge-secondary am-margin-right am-fr"></span></a></li> -->
			<!-- <li>
			  <a href="/manage/liarpost"><span class="am-icon-pencil-square-o"></span> <?php if(session('admin')) echo('提交骗子<span class="am-badge am-badge-secondary am-margin-right am-fr">管理</span>'); 		
				  else echo('举报骗子'); 
			  ?></a>
			</li> -->
			<?php if(isset($_SESSION['admin']) && $_SESSION['admin']): 			try {
				$adminMenuItems = app\manage\model\UserAdmin::GetItems();
				if (!empty($adminMenuItems)) {
					foreach ($adminMenuItems as $item) {?>
						<li><a href="<?php echo $item['url']; ?>"><span class="<?php echo $item['icon']; ?>"></span> <?php echo $item['itemName']; ?></a></li>
					<?php }
				} else { ?>
					<li><a href="#"><span class="am-icon-warning"></span> 暂无管理菜单</a></li>
				<?php }
			} catch (Exception $e) { ?>
				<li><a href="#"><span class="am-icon-exclamation-triangle"></span> 菜单加载失败</a></li>
			<?php } ?>
			<?php endif; ?>
			<li><a href="/manage/profile"><span class="am-icon-gift"></span> 自定义资料</a></li>
			<!-- <li><a href="/manage/help"><span class="am-icon-map-signs"></span> 防骗指南</a></li> -->
			<!-- <li><a href="/manage/choose"><span class="am-icon-paint-brush"></span> 安全测试</a></li> -->
			<?php if(session('user')){ ?> <li><a href="/manage/logout"><span class="am-icon-sign-out"></span> 注销</a></li> <?php } ?>
		  </ul>
		  
		  <div class="am-panel am-panel-default admin-sidebar-panel">
			<div class="am-panel-bd">
			  <p><span class="am-icon-bookmark"></span> 公告</p>
			  <div class="line">
                    <span>萌新必看：</span>
                    <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/9839e342f1dd89219e2e2980a9a803a42b9d94cf">2.0.1 使用指南</a></p>
			        <p><a class="home" target="_blank" href="https://docs.qq.com/doc/p/7ea1c83b63c8b59b7472c999d15156c4fb843d31">3.0 新版使用指南</a></p>
			        
               </div>
			  
			  <div class="line">
                    <span>剑灵小助手[3.0]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/94255b808597">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1qcuS-obYbHKBQAuuH6_Bhw?pwd=5210">百度网盘</a>
                    </p>
               </div>
               <div class="line">
                    <span>剑灵小助手[2.0.1]：</span>
                    <p>
                    <a class="home" target="_blank" href="https://pan.quark.cn/s/2ad567e2b816#/list/share">夸克网盘</a>
                    <a class="home" target="_blank" href="https://pan.baidu.com/s/1irL97YxJfR1-UWxkxqXHIQ?pwd=5210">百度网盘</a>
                    <a class="home" target="_blank" href="https://www.lanzoul.com/iUyTU1irepef">蓝奏云</a>
                    </p>
                </div>
			  
			  <div class="texts" style="margin-top: 20px;">
                    <p><span>助手①群：<code>548810086</code></span></p>
                    <p><span>助手②群：<code>563768233</code></span></p>
                    <p><span>助手③群：<code>618986361</code></span></p>
                </div>
			</div>
		  </div>
		</div>
	  </div>

	  <!-- 移动设备侧边栏遮罩层 -->
	  <div class="mobile-sidebar-overlay" onclick="hideMobileSidebar()"></div>

	  

<div class="admin-content">
    <div class="admin-content-body">
        <div class="am-cf am-padding am-padding-bottom-0">
            <div class="am-fl am-cf">
                <strong class="am-text-primary am-text-lg">更新配置管理</strong> /
                <small>Update Config Management</small>
            </div>
        </div>

        <hr>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-panel am-panel-default">
                    <div class="am-panel-hd">
                        <h3 class="am-panel-title">
                            <i class="am-icon-cog"></i> 更新配置列表
                            <div class="am-fr">
                                <a href="/manage/admin/update-config/edit" class="am-btn am-btn-primary am-btn-xs">
                                    <i class="am-icon-plus"></i> 添加配置
                                </a>
                            </div>
                        </h3>
                    </div>

                    <div class="am-panel-bd">
                        <?php if(isset($configs) && count($configs) > 0): ?>
                        <div class="am-scrollable-horizontal">
                            <table class="am-table am-table-striped am-table-hover am-table-bd am-table-bdrs am-table-centered">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>应用名称</th>
                                        <th>当前版本</th>
                                        <th>可执行文件</th>
                                        <th>下载链接</th>
                                        <th>插件版本</th>
                                        <th>测试版本</th>
                                        <th>状态</th>
                                        <th>群组数量</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                            <tbody>
                                <?php if(is_array($configs) || $configs instanceof \think\Collection || $configs instanceof \think\Paginator): $i = 0; $__LIST__ = $configs;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$config): $mod = ($i % 2 );++$i;?>
                                <tr>
                                    <td><?php echo htmlentities((string) $config['id']); ?></td>
                                    <td>
                                        <strong class="am-text-primary"><?php echo htmlentities((string) $config['name']); ?></strong>
                                    </td>
                                    <td>
                                        <span class="am-badge am-badge-primary"><?php echo htmlentities((string) $config['version']); ?></span>
                                    </td>
                                    <td><code><?php echo htmlentities((string) $config['executable_path']); ?></code></td>
                                    <td>
                                        <?php if($config['url']): ?>
                                        <a href="<?php echo htmlentities((string) $config['url']); ?>" target="_blank" class="am-text-primary">
                                            <i class="am-icon-download"></i> 下载
                                        </a>
                                        <?php else: ?>
                                        <span class="am-text-muted">未设置</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($config['plugin_version']): ?>
                                        <span class="am-badge am-badge-secondary"><?php echo htmlentities((string) $config['plugin_version']); ?></span>
                                        <?php else: ?>
                                        <span class="am-text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($config['beta']): ?>
                                        <span class="am-badge am-badge-warning">是</span>
                                        <?php else: ?>
                                        <span class="am-badge am-badge-success">否</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($config['is_active']): ?>
                                        <span class="am-badge am-badge-success">启用</span>
                                        <?php else: ?>
                                        <span class="am-badge am-badge-danger">禁用</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="am-badge am-badge-primary"><?php echo htmlentities((string) $config['group_count']); ?></span>
                                    </td>
                                    <td><?php echo htmlentities((string) $config['created_at']); ?></td>
                                    <td>
                                        <div class="am-btn-group am-btn-group-xs">
                                            <a href="/manage/admin/update-config/edit?id=<?php echo htmlentities((string) $config['id']); ?>"
                                               class="am-btn am-btn-default" title="编辑">
                                                <i class="am-icon-edit"></i>
                                            </a>
                                            <a href="/manage/admin/update-config/groups?name=<?php echo htmlentities((string) $config['name']); ?>"
                                               class="am-btn am-btn-secondary" title="群组管理">
                                                <i class="am-icon-users"></i>
                                            </a>
                                            <button type="button" class="am-btn am-btn-danger"
                                                    onclick="deleteConfig(<?php echo htmlentities((string) $config['id']); ?>)" title="删除">
                                                <i class="am-icon-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="am-text-center" style="padding: 60px 0;">
                        <i class="am-icon-inbox am-icon-lg am-text-muted"></i>
                        <h4 class="am-text-muted">暂无更新配置</h4>
                        <p class="am-text-muted">点击上方"添加配置"按钮创建第一个更新配置</p>
                        <a href="/manage/admin/update-config/edit" class="am-btn am-btn-primary">
                            <i class="am-icon-plus"></i> 添加配置
                        </a>
                    </div>
                    <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 强制更新控制面板 -->
    <div class="am-panel am-panel-default">
        <div class="am-panel-hd">
            <h3 class="am-panel-title">
                <i class="am-icon-exclamation-triangle am-text-warning"></i> 强制更新控制
            </h3>
        </div>
        <div class="am-panel-bd">
            <div class="am-g">
                <div class="am-u-md-6">
                    <div class="am-form-group">
                        <label>应用名称:</label>
                        <input type="text" id="forceUpdateAppName" class="am-form-field" value="bns-helper" readonly>
                    </div>
                </div>
                <div class="am-u-md-6">
                    <div class="am-form-group">
                        <label>当前状态:</label>
                        <div>
                            <span id="forceUpdateStatus" class="am-badge am-badge-secondary">检查中...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="am-g">
                <div class="am-u-md-12">
                    <div class="am-alert am-alert-secondary">
                        <i class="am-icon-info-circle"></i>
                        <strong>说明:</strong> 启用强制更新后，所有在线客户端将在下次心跳时收到更新通知并自动退出进行更新。
                    </div>
                </div>
            </div>
            <div class="am-g">
                <div class="am-u-md-12">
                    <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="setForceUpdate(true)">
                        <i class="am-icon-power-off"></i> 启用强制更新
                    </button>
                    <button type="button" class="am-btn am-btn-success am-btn-sm" onclick="setForceUpdate(false)">
                        <i class="am-icon-check"></i> 清除强制更新
                    </button>
                    <button type="button" class="am-btn am-btn-secondary am-btn-sm" onclick="checkForceUpdateStatus()">
                        <i class="am-icon-refresh"></i> 刷新状态
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 删除配置
function deleteConfig(id) {
    layer.confirm('确定要删除这个配置吗？', {
        icon: 3,
        title: '确认删除'
    }, function(index) {
        $.ajax({
            url: '/manage/admin/update-config/delete',
            method: 'POST',
            data: { id: id },
            success: function(res) {
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1});
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 设置强制更新
function setForceUpdate(force) {
    const appName = document.getElementById('forceUpdateAppName').value;
    const action = force ? '启用' : '清除';

    layer.confirm(`确定要${action}强制更新吗？`, {
        icon: 3,
        title: '确认操作'
    }, function(index) {
        $.ajax({
            url: '/manage/admin/update-config/setForceUpdate',
            method: 'POST',
            data: {
                app_name: appName,
                force: force
            },
            success: function(res) {
                if (res.code === 1) {
                    layer.msg(res.msg, {icon: 1});
                    checkForceUpdateStatus(); // 刷新状态
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        layer.close(index);
    });
}

// 检查强制更新状态
function checkForceUpdateStatus() {
    const appName = document.getElementById('forceUpdateAppName').value;

    $.ajax({
        url: '/manage/admin/update-config/getForceUpdateStatus',
        method: 'GET',
        data: { app_name: appName },
        success: function(res) {
            if (res.code === 1) {
                const status = res.data.force_update;
                const statusElement = document.getElementById('forceUpdateStatus');

                if (status) {
                    statusElement.className = 'am-badge am-badge-danger';
                    statusElement.textContent = '强制更新已启用';
                } else {
                    statusElement.className = 'am-badge am-badge-success';
                    statusElement.textContent = '正常状态';
                }
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        },
        error: function() {
            layer.msg('获取状态失败', {icon: 2});
        }
    });
}

// 页面加载时检查状态
$(document).ready(function() {
    checkForceUpdateStatus();
});
</script>

	   
	  <div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
		<div id="innerdiv" style="position:absolute;">
		  <img id="bigimg" style="border:5px solid #fff;" src="" />
		</div>
	  </div>
	  <footer class="admin-content-footer">
		<hr>
		<p class="am-padding-left">© 2018 duoluosb.</p>
	  </footer>
	</div>

	<script>
	// 页面初始化脚本

	// 窗口大小改变时隐藏移动侧边栏
	window.addEventListener('resize', function() {
		if (window.innerWidth > 640) {
			hideMobileSidebar();
		}
	});

	// 页面加载完成后的初始化
	document.addEventListener('DOMContentLoaded', function() {
		// 添加触摸事件支持
		var toggleBtn = document.querySelector('.mobile-sidebar-toggle');
		if (toggleBtn) {
			toggleBtn.addEventListener('touchstart', function(e) {
				e.preventDefault();
				toggleMobileSidebar();
			});
		}
	});
	</script>
</body>

<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?00e000ae4edf31394d2153c309efbdec";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
</script>