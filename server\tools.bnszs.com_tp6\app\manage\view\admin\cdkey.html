{layout name="manage/template" /}

<div class="admin-content">
	<div class="admin-content-body">
		<div class="am-cf am-padding">
			<div class="am-fl am-cf"><strong class="am-text-primary am-text-lg">首页</strong> / <small>CDkey生成</small></div>
			<div class="am-fr">
				<a href="/manage/admin/downloads" class="am-btn am-btn-secondary am-btn-sm">
					<span class="am-icon-download"></span> 下载管理
				</a>
			</div>
		</div>
		<div>
			<form class="am-form am-form-horizontal" method="post" id="InfoForm" style='text-align: center;'>
				<div class="am-form-group" id="type">
					<label for="type" class="am-u-sm-3 am-form-label">选择类型</label>
					<div class="am-u-sm-9">
						<select id="type">
							<option value="customize">● 装备查询自定义 （此功能已调整为使用就永久)</option>
							<option value="client">● 装备向导时长</option>
							<option value="drawtimes">● 抽奖次数</option>
						</select>
						<small id="type_msg"></small>
					</div>
				</div>
				<div class="am-form-group" id="specified-info">
					<div class="am-form-group" id="time-limit-type">
						<label for="time-limit-type" class="am-u-sm-3 am-form-label">选择期限类型</label>
						<div class="am-u-sm-9">
							<select id="time-limit-type">
								<option value="fixed">● 固定日期结束</option>
								<option value="duration">● 激活后开始计算天数</option>
							</select>
							<small id="time-limit-type_msg"></small>
						</div>
					</div>
					<!-- 激活天数方式，默认隐藏 -->
					<div class="am-form-group" style="display:none" id="usable-duration">
						<label for="usable-duration" class="am-u-sm-3 am-form-label">激活天数</label>
						<div class="am-u-sm-9">
							<input type="text" id="usable-duration" name="usable-duration" value="" placeholder="激活天数，从用户激活时开始计算结束时间。" required />
							<small id="addTime_msg"></small>
						</div>
					</div>
					<!-- 固定时间方式 -->
					<div class="am-form-group" id="fixed-expiration-time">
						<label for="fixed-expiration-time" class="am-u-sm-3 am-form-label">过期时间</label>
						<div class="am-u-sm-9">
							<input type="date" id="fixed-expiration-time" name="fixed-expiration-time" value="" placeholder="页面于何时固定过期" required />
							<small id="toDate_msg"></small>
						</div>
					</div>
					<div class="am-form-group" style="display:none" id="specified-value">
						<label for="specified-value" class="am-u-sm-3 am-form-label">数值</label>
						<div class="am-u-sm-9">
							<input type="text" id="specified-value" name="specified-value" value="" placeholder="" required />
							<small id="toDate_msg"></small>
						</div>
					</div>
				</div>
				<div class="am-form-group" id="changeTimes">
					<label for="changeTimes" class="am-u-sm-3 am-form-label">∞可修改次数</label>
					<div class="am-u-sm-9">
						<input type="text" id="changeTimes" name="changeTimes" value="" placeholder="可修改次数，≤3 或不填 都默认为3次，填写∞则无限" required>
						<small id="changeTimes_msg"></small>
					</div>
				</div>
				<div class="am-form-group" id="usableTimes">
					<label for="usableTimes" class="am-u-sm-3 am-form-label">可使用次数</label>
					<div class="am-u-sm-9">
						<input type="text" id="usableTimes" name="usableTimes" value="1" placeholder="批量生成时固定为1次" readonly style="background-color: #f5f5f5;">
						<small id="usableTimes_msg" style="color: #ff6600;">注意：批量生成时可使用次数固定为1次</small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="startTime" class="am-u-sm-3 am-form-label">激活开放时间</label>
					<div class="am-u-sm-9">
						<input type="date" id="startTime" name="startTime" value="" placeholder="激活截止时间" required>
						<small id="startTime_msg"></small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="endTime" class="am-u-sm-3 am-form-label">激活结束时间</label>
					<div class="am-u-sm-9">
						<input type="date" id="endTime" name="endTime" value="" placeholder="激活截止时间" required>
						<small id="endTime_msg"></small>
					</div>
				</div>
				<!-- 批次字段已隐藏，批量生成不使用批次信息 -->
				<div class="am-form-group">
					<label for="num" class="am-u-sm-3 am-form-label">生成个数</label>
					<div class="am-u-sm-9">
						<input type="text" id="num" name="num" value="" placeholder="生成个数,不填写默认1个,一次生成不得大于5000个" required>
						<small id="num_msg"></small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="discern" class="am-u-sm-3 am-form-label">识别前缀</label>
					<div class="am-u-sm-9">
						<input type="text" id="discern" name="discern" value="" placeholder="用于追加在CDkey前的标识,方便区分CDkey来源" required>
						<small id="discern_msg" style="color: #0066cc;">
							智能建议：
							<a href="javascript:void(0)" onclick="setPrefix('VIP')" class="prefix-suggestion">VIP</a> |
							<a href="javascript:void(0)" onclick="setPrefix('GIFT')" class="prefix-suggestion">GIFT</a> |
							<a href="javascript:void(0)" onclick="setPrefix('EVENT')" class="prefix-suggestion">EVENT</a> |
							<a href="javascript:void(0)" onclick="setPrefix('PROMO')" class="prefix-suggestion">PROMO</a> |
							<a href="javascript:void(0)" onclick="setPrefix('TEST')" class="prefix-suggestion">TEST</a>
						</small>
					</div>
				</div>
				<div class="am-form-group">
					<label for="remark" class="am-u-sm-3 am-form-label">备注</label>
					<div class="am-u-sm-9">
						<textarea type="text" id="remark" name="remark"></textarea>
						<small id="remark_msg"></small>
					</div>
				</div>
				<button type="button" id="Btn_Submit" onclick="create();" class="am-btn am-btn-primary">生成</button>
			</form>
		</div>
		<hr>
		<div class="am-g">
			<div class="am-u-sm-12 am-u-md-3">
				<div class="am-input-group am-input-group-sm">
					<input type="text" class="am-form-field" id="search_value" placeholder="输入需要查询的信息">
					<span class="am-input-group-btn">
						<button class="am-btn am-btn-default" type="button" id="search" onclick="search();">查询</button>
					</span>
				</div>
			</div>
			<div class="am-select am-u-sm-9">
				<select class="ModeSelect">
					<option value="0">全部</option>
					<option value="1">当前可用</option>
					<option value="2">当前不可用</option>
				</select>
				<span style="padding-left:20px;"><input type="checkbox" class="show-alluser" style="margin-left:10px;" />查看所有（不勾选表示只显示自己）</p> </span>
			</div>
		</div>
		<div class="am-g">
			<div class="am-u-sm-12">
				<table class="am-table am-table-bd am-table-striped admin-content-table">
					<thead>
						<tr>
							<th>ID</th>
							<th>CDkey</th>
							<th>生成者</th>
							<th>总次数</th>
							<th>当前剩余</th>
							<th>激活开始时间</th>
							<th>激活截止时间</th>
							<th>类型</th>
							<th>操作</th>
						</tr>
					</thead>
					<tbody>
					{volist name="data" id="item"}
						<tr>
							<td>{$item.id}</td>
							<td>{$item.cdkey}</td>
							<td>{$item.username ?? "系统"}</td>
							<td>{$item.MaxNum}</td>
							<td>{$item.RemainNum}</td>
							<td>{$item.startTime}</td>
							<td>{$item.endTime}</td>
							<td>{$item.type}</td>
							<td>
								<div class="am-btn-group am-btn-group-xs">
									<a href="?id={$item.id}" class="am-btn am-btn-default am-btn-xs am-text-secondary"><span class="am-icon-pencil-square-o"></span> 编辑</a>
								</div>
							</td>
						</tr>
					{/volist}
                    </tbody>
				</table>
			</div>
		</div>
    </div>
</div>

<style>
.prefix-suggestion {
	color: #0066cc;
	text-decoration: none;
	padding: 2px 4px;
	border-radius: 3px;
	background-color: #f0f8ff;
	margin: 0 2px;
	font-size: 12px;
}

.prefix-suggestion:hover {
	background-color: #0066cc;
	color: white;
	text-decoration: none;
}

#discern_msg {
	margin-top: 5px;
	font-size: 12px;
}
</style>

<script>
	//回车事件绑定		
	$('#search_value').bind('keypress', function(event) {
		if (event.keyCode == "13") {
			event.preventDefault();
	
			//回车执行查询
			search();
		}
	});
	
	function create() {
	
		if (!confirm("是否确认进行操作?")) return;
	
		$.ajax({
			type: "POST", //方法类型
			dataType: "json", //预期服务器返回的数据类型
			url: window.location.href, //url
			data: $('#InfoForm').serialize() 
				+ "&type=" + $('select#type').val()
				+ "&time-limit-type=" + $('select#time-limit-type').val(),
	
			success: function(result) {
				if (result.code) {
					var message = result.msg;
					if (result.download_url) {
						message += "\n\n点击确定后将自动下载CDkey文件";
					}
					alert(message);

					// 如果有下载链接，自动下载文件
					if (result.download_url) {
						var link = document.createElement('a');
						link.href = result.download_url;
						link.download = result.filename || 'cdkeys.txt';
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
					}

					location.reload();
				} else {
					alert(result.msg);
				}
			},
			error: function() {
				alert("提交失败,未知错误");
			}
		});
	}

	// 设置识别前缀
	function setPrefix(prefix) {
		$('#discern').val(prefix);
		$('#discern').focus();
	}

	// 智能前缀建议
	function updatePrefixSuggestions() {
		var type = $('#type').val();
		var suggestions = [];

		switch(type) {
			case 'customize':
				suggestions = ['VIP', 'CUSTOM', 'SPECIAL', 'PREMIUM', 'GOLD'];
				break;
			case 'client':
				suggestions = ['CLIENT', 'EQUIP', 'GEAR', 'TOOL', 'HELPER'];
				break;
			case 'drawtimes':
				suggestions = ['DRAW', 'LUCKY', 'LOTTERY', 'CHANCE', 'SPIN'];
				break;
			default:
				suggestions = ['VIP', 'GIFT', 'EVENT', 'PROMO', 'TEST'];
		}

		var html = '智能建议：';
		for (var i = 0; i < suggestions.length; i++) {
			html += '<a href="javascript:void(0)" onclick="setPrefix(\'' + suggestions[i] + '\')" class="prefix-suggestion">' + suggestions[i] + '</a>';
			if (i < suggestions.length - 1) html += ' | ';
		}

		$('#discern_msg').html(html);
	}

	// 自动生成带时间戳的前缀
	function generateTimestampPrefix() {
		var type = $('#type').val();
		var typePrefix = '';

		switch(type) {
			case 'customize': typePrefix = 'CUSTOM'; break;
			case 'client': typePrefix = 'CLIENT'; break;
			case 'drawtimes': typePrefix = 'DRAW'; break;
			default: typePrefix = 'CDK';
		}

		var now = new Date();
		var timestamp = (now.getMonth() + 1).toString().padStart(2, '0') +
						now.getDate().toString().padStart(2, '0') +
						now.getHours().toString().padStart(2, '0') +
						now.getMinutes().toString().padStart(2, '0');

		return typePrefix + timestamp;
	}


	function search() {
		var search_value = $("#search_value").val();
	
		if (search_value == null || search_value == undefined || search_value == '') {
	
			alert("请输入查询条件 如服务器:无日峰 或角色名称 以及任意你知道的信息");
			return false;
	
		} else {
	
			window.location.href = "?search=" + search_value;
	
		}
	}
	

	$('select#type').change(function() {
		$('.am-form-group #specified-info').hide();
		$('#changeTimes').hide();
		$('#specified-value').hide();

		switch ($(this).val()) {
			case 'customize':
				$('#time-limit-type').show();
				$('#changeTimes').show();
				break;

			case 'client':
				$('#time-limit-type').show();
				break;

			case 'drawtimes':
				$('#specified-value').show();
				break;
		}

		// 更新智能前缀建议
		updatePrefixSuggestions();

		// 如果前缀为空，自动填充智能前缀
		if ($('#discern').val() === '') {
			$('#discern').val(generateTimestampPrefix());
		}
	})
	
	//时长控制控件
	$('select#time-limit-type').change(function() {
		
		var DurationGroup = $('div#usable-duration');
		var FixedGroup = $('div#fixed-expiration-time');
	
		DurationGroup.hide();
		FixedGroup.hide();
	
		//根据情况显示组
		switch ($(this).val()) {
			case 'duration': DurationGroup.show(); break;
			case 'fixed': FixedGroup.show(); break;
		}
	})
	
	
	//*******************************************************************//
	var _progress = false;
	
	function init() {
	
		_progress = true;
	
		var mode = getQueryVariable("mode");
		var all  = getQueryVariable("all") == "true";
	
		$(".ModeSelect").find("option").each(function() {
			if ($(this).val() == mode) {
				$(".ModeSelect").val($(this).val()).trigger('change');
				return;
			}
		})
	
		//如果显示所有生成
		$(".show-alluser").attr("checked", all);

		// 初始化智能前缀建议
		updatePrefixSuggestions();

		// 如果前缀为空，自动填充智能前缀
		if ($('#discern').val() === '') {
			$('#discern').val(generateTimestampPrefix());
		}

		_progress = false;
	}
	
	init();
	//*******************************************************************//

	// 实现模式切换
	$('.ModeSelect,.show-alluser').change(function() {
		if (_progress) return;
	
		var mode = $(".ModeSelect").val();
		var all = $(".show-alluser").is(':checked');
	
		if (mode == null || mode == "" || mode == "undefined") location.reload();
		else window.location.href = "?mode=" + mode + "&all=" + all;
	})
</script>

/* 表格区域样式 */
.admin-content-table {
    width: 100%;
    margin-top: 20px;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-content-table thead th {
    background: #f8f9fa;
    padding: 15px;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #eee;
    text-align: left;
}

.admin-content-table tbody td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    color: #4a5568;
    font-size: 14px;
}

.admin-content-table tbody tr:hover {
    background: #f8f9fa;
}

.admin-content-table tbody tr:last-child td {
    border-bottom: none;
}

/* 操作按钮样式 */
.am-btn-group-xs .am-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.3s;
}

.am-text-secondary {
    color: #0e90d2;
    background: rgba(14,144,210,0.1);
    border: none;
}

.am-text-secondary:hover {
    background: rgba(14,144,210,0.2);
    color: #0e90d2;
}

/* 搜索框样式 */
.am-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.am-input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s;
}

.am-input-group input:focus {
    border-color: #0e90d2;
    box-shadow: 0 0 5px rgba(14,144,210,0.2);
    outline: none;
}

.am-input-group .am-btn {
    padding: 10px 20px;
    background: #0e90d2;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.am-input-group .am-btn:hover {
    background: #0d82bd;
}

/* 筛选区域样式 */
.am-select {
    display: flex;
    align-items: center;
    gap: 15px;
}

.ModeSelect {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    font-size: 14px;
}

.show-alluser {
    margin-left: 10px;
    accent-color: #0e90d2;
}

/* 修复颜色样式 */
.admin-content-table tbody tr {
    color: #333;
}